﻿using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Net.Mail;
using Cast_Stone_api.DTOs.Request;
using Cast_Stone_api.Services;

[ApiController]
[Route("api/[controller]")]
public class EmailController : ControllerBase
{
    private readonly IConfiguration _config;
    private readonly IEmailService _emailService;
    private readonly ILogger<EmailController> _logger;

    public EmailController(IConfiguration config, IEmailService emailService, ILogger<EmailController> logger)
    {
        _config = config;
        _emailService = emailService;
        _logger = logger;
    }

    [HttpPost("send")]
    public IActionResult SendEmail([FromBody] EmailRequest request)
    {
        var smtpSettings = _config.GetSection("SmtpSettings");

        using var client = new SmtpClient(smtpSettings["Host"], int.Parse(smtpSettings["Port"]))
        {
            Credentials = new NetworkCredential(smtpSettings["Username"], smtpSettings["Password"]),
            EnableSsl = bool.Parse(smtpSettings["EnableSsl"]),
        };

        var message = new MailMessage
        {
            From = new MailAddress(smtpSettings["FromEmail"], smtpSettings["FromName"]),
            Subject = request.Subject,
            Body = request.Message,
            IsBodyHtml = true
        };

        message.To.Add(smtpSettings["AdminEmail"]);

        client.Send(message);

        return Ok(new { status = "sent" });
    }

    [HttpPost("contact-form-reply")]
    public async Task<IActionResult> SendContactFormAutoReply([FromBody] ContactFormAutoReplyRequest request)
    {
        _logger.LogInformation("📧 Email API: Contact form auto-reply request for {UserEmail} ({UserName})",
            request.UserEmail, request.UserName);

        try
        {
            var result = await _emailService.SendContactFormAutoReplyAsync(
                request.UserEmail,
                request.UserName,
                request.InquiryType,
                request.Message,
                request.Company,
                request.State,
                request.PhoneNumber
            );

            if (result.Success)
            {
                _logger.LogInformation("✅ Email API: Contact form auto-reply sent successfully to {UserEmail}", request.UserEmail);
                return Ok(new { status = "sent", message = result.Message });
            }
            else
            {
                _logger.LogWarning("⚠️ Email API: Contact form auto-reply failed for {UserEmail}: {Message}",
                    request.UserEmail, result.Message);
                return BadRequest(new { status = "failed", message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Email API: Exception sending contact form auto-reply to {UserEmail}", request.UserEmail);
            return StatusCode(500, new { status = "error", message = ex.Message });
        }
    }

    [HttpPost("order-confirmation")]
    public async Task<IActionResult> SendOrderConfirmation([FromBody] OrderConfirmationRequest request)
    {
        try
        {
            var result = await _emailService.SendOrderConfirmationToCustomerAsync(
                request.CustomerEmail,
                request.CustomerName,
                request.OrderId,
                request.TotalAmount,
                request.OrderItems,
                request.PaymentMethod,
                request.ShippingAddress
            );

            if (result.Success)
            {
                return Ok(new { status = "sent", message = result.Message });
            }
            else
            {
                return BadRequest(new { status = "failed", message = result.Message });
            }
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { status = "error", message = ex.Message });
        }
    }

    [HttpGet("test-config")]
    public IActionResult TestEmailConfiguration()
    {
        _logger.LogInformation("🔧 Testing email configuration...");

        try
        {
            var smtpSettings = _config.GetSection("SmtpSettings");

            var configInfo = new
            {
                Host = smtpSettings["Host"],
                Port = smtpSettings["Port"],
                EnableSsl = smtpSettings["EnableSsl"],
                FromEmail = smtpSettings["FromEmail"],
                FromName = smtpSettings["FromName"],
                AdminEmail = smtpSettings["AdminEmail"],
                HasUsername = !string.IsNullOrEmpty(smtpSettings["Username"]),
                HasPassword = !string.IsNullOrEmpty(smtpSettings["Password"])
            };

            _logger.LogInformation("📧 SMTP Configuration: {@ConfigInfo}", configInfo);

            return Ok(new {
                status = "config-retrieved",
                message = "Email configuration retrieved successfully",
                config = configInfo
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error retrieving email configuration");
            return StatusCode(500, new { status = "error", message = ex.Message });
        }
    }

    [HttpPost("test-send")]
    public async Task<IActionResult> TestEmailSend([FromBody] TestEmailRequest request)
    {
        _logger.LogInformation("🧪 Testing email send to {Email}", request.Email);

        try
        {
            var result = await _emailService.SendEmailAsync(
                request.Email,
                "Test Email from Cast Stone API",
                "<h1>Test Email</h1><p>This is a test email from the Cast Stone API to verify email functionality.</p>",
                "Test Email\n\nThis is a test email from the Cast Stone API to verify email functionality."
            );

            if (result.Success)
            {
                _logger.LogInformation("✅ Test email sent successfully to {Email}", request.Email);
                return Ok(new { status = "sent", message = result.Message });
            }
            else
            {
                _logger.LogWarning("⚠️ Test email failed for {Email}: {Message}", request.Email, result.Message);
                return BadRequest(new { status = "failed", message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Exception sending test email to {Email}", request.Email);
            return StatusCode(500, new { status = "error", message = ex.Message });
        }
    }
}

public class TestEmailRequest
{
    public string Email { get; set; } = string.Empty;
}
