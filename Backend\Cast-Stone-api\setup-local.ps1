# Cast Stone API - Local Setup Script
# This script helps set up the local development environment

Write-Host "🚀 Cast Stone API - Local Development Setup" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Check if .NET 8 is installed
Write-Host "`n📋 Checking prerequisites..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK not found. Please install .NET 8 SDK from https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Red
    exit 1
}

# Check if PostgreSQL is accessible
Write-Host "`n🔍 Checking PostgreSQL connection..." -ForegroundColor Yellow
$connectionString = "Host=localhost;Port=5432;Database=postgres;Username=postgres;Password=password;SSL Mode=Disable"

# Test PostgreSQL connection (basic check)
try {
    # This is a simple check - in a real scenario you might want to use a more robust method
    Write-Host "📝 Please ensure PostgreSQL is installed and running on localhost:5432" -ForegroundColor Cyan
    Write-Host "📝 Default credentials: username=postgres, password=password" -ForegroundColor Cyan
    Write-Host "📝 Database name: caststone_local" -ForegroundColor Cyan
} catch {
    Write-Host "⚠️  Could not verify PostgreSQL connection. Please ensure PostgreSQL is installed and running." -ForegroundColor Yellow
}

# Create database (user will need to do this manually)
Write-Host "`n📊 Database Setup Instructions:" -ForegroundColor Yellow
Write-Host "1. Connect to PostgreSQL using pgAdmin or psql" -ForegroundColor Cyan
Write-Host "2. Run: CREATE DATABASE caststone_local;" -ForegroundColor Cyan
Write-Host "3. Or use the following command in psql:" -ForegroundColor Cyan
Write-Host "   psql -U postgres -c `"CREATE DATABASE caststone_local;`"" -ForegroundColor Gray

# Ask user if they want to continue
$continue = Read-Host "`nHave you created the 'caststone_local' database? (y/n)"
if ($continue -ne "y" -and $continue -ne "Y") {
    Write-Host "Please create the database first and run this script again." -ForegroundColor Yellow
    exit 0
}

# Run Entity Framework migrations
Write-Host "`n🔄 Running Entity Framework migrations..." -ForegroundColor Yellow
try {
    $env:ASPNETCORE_ENVIRONMENT = "Local"
    dotnet ef database update --environment Local
    Write-Host "✅ Database migrations completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Migration failed. Please check your database connection and try again." -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "`n🔨 Building the project..." -ForegroundColor Yellow
try {
    dotnet build
    Write-Host "✅ Project built successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed. Please check for compilation errors." -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 Local setup completed successfully!" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Run the application using one of these methods:" -ForegroundColor Cyan
Write-Host "   • dotnet run --environment Local" -ForegroundColor Gray
Write-Host "   • Use Visual Studio and select 'Local-HTTP' or 'Local-HTTPS' profile" -ForegroundColor Gray
Write-Host "`n2. Access the API at:" -ForegroundColor Cyan
Write-Host "   • HTTP: http://localhost:7069" -ForegroundColor Gray
Write-Host "   • HTTPS: https://localhost:5090" -ForegroundColor Gray
Write-Host "   • Swagger: http://localhost:7069/swagger" -ForegroundColor Gray
Write-Host "`n3. The API will use the local PostgreSQL database 'caststone_local'" -ForegroundColor Cyan

Write-Host "`n🔧 Configuration Files:" -ForegroundColor Yellow
Write-Host "• appsettings.Local.json - Local environment settings" -ForegroundColor Gray
Write-Host "• Properties/launchSettings.json - Updated with local profiles" -ForegroundColor Gray
Write-Host "• All Railway configurations remain unchanged" -ForegroundColor Gray
