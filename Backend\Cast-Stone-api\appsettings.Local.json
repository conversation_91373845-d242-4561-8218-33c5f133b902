{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Cast_Stone_api.Services.EmailService": "Debug", "Cast_Stone_api.Controllers.ContactFormController": "Debug", "Cast_Stone_api.Controllers.EmailController": "Debug", "Cast_Stone_api.Services.Implementations.ContactFormSubmissionService": "Debug", "MailKit": "Debug", "System.Net.Mail": "Debug"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=metro.proxy.rlwy.net;Port=26318;Database=railway;Username=postgres;Password=********************************;SSL Mode=Require;Trust Server Certificate=true"}, "Cloudinary": {"CloudName": "dp6u85kkx", "ApiKey": "257221466619946", "ApiSecret": "JYo-xIQQeywLhircNkyLPUgjhFw"}, "SmtpSettings": {"Host": "smtp-relay.brevo.com", "Port": 587, "Username": "<EMAIL>", "Password": "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", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "Cast Stone", "AdminEmail": "<EMAIL>"}, "Stripe": {"SecretKey": "sk_test_51RiY264KIXKpSzUcHySFVQOQUDQTkDivJC0b5ACyt8IX0Mcfq8vzcJtPNNsjehjiTuWLg0tf3HxjoRIIjiaicqF400J0197z6Y", "PublishableKey": "pk_test_51RiY264KIXKpSzUcJ1GJhfa5qmdsAKLNti3717RDHO2xazuUo3mqm5YjQXFKa7LYDoyFgcccDfeBQHJvjE8yFRFK00OsYl0tkV"}, "PayPal": {"ClientId": "your-paypal-client-id", "Secret": "your-paypal-secret", "Environment": "sandbox"}, "ApplePay": {"MerchantId": "merchant.com.example", "DomainVerificationPath": ".well-known/apple-developer-merchantid-domain-association"}}