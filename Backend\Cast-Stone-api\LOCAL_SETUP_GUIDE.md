# Cast Stone API - Local Development Setup

This guide will help you set up the Cast Stone API for local development without affecting the existing Railway deployment configuration.

## Prerequisites

1. **PostgreSQL** - Install PostgreSQL locally
   - Download from: https://www.postgresql.org/download/
   - Default port: 5432
   - Create a database named: `caststone_local`
   - Default credentials: username=`postgres`, password=`password`

2. **.NET 8 SDK** - Make sure you have .NET 8 installed
   - Download from: https://dotnet.microsoft.com/download/dotnet/8.0

## Local Configuration Files

The following files have been created for local development:

### 1. `appsettings.Local.json`
- Contains local database connection string
- Uses test Stripe keys (already configured)
- Uses same Cloudinary and email settings as development

### 2. Updated `launchSettings.json`
- Added `Local-HTTP` profile (runs on http://localhost:7069)
- Added `Local-HTTPS` profile (runs on https://localhost:5090 and http://localhost:7069)
- Existing Railway profiles remain unchanged

### 3. Updated `Program.cs`
- Added environment check for "Local" environment
- Local environment uses port 7069 as requested
- Railway configuration remains unchanged

## Setup Steps

### 1. Install PostgreSQL
```bash
# Windows (using chocolatey)
choco install postgresql

# Or download installer from postgresql.org
```

### 2. Create Local Database
```sql
-- Connect to PostgreSQL as postgres user
CREATE DATABASE caststone_local;
```

### 3. Update Database Connection (if needed)
If your PostgreSQL setup differs from defaults, update the connection string in `appsettings.Local.json`:
```json
"ConnectionStrings": {
  "DefaultConnection": "Host=localhost;Port=5432;Database=caststone_local;Username=your_username;Password=your_password;SSL Mode=Disable"
}
```

### 4. Run Database Migrations
```bash
cd Backend/Cast-Stone-api
dotnet ef database update --environment Local
```

### 5. Run the Application
```bash
# Option 1: Using dotnet CLI with Local environment
dotnet run --environment Local

# Option 2: Using Visual Studio
# Select "Local-HTTP" or "Local-HTTPS" profile from the dropdown and run
```

## Available Endpoints

When running locally, the API will be available at:
- HTTP: http://localhost:7069
- HTTPS: https://localhost:5090
- Swagger UI: http://localhost:7069/swagger or https://localhost:5090/swagger

## Environment Profiles

- **Local-HTTP**: Runs on port 7069 (HTTP only)
- **Local-HTTPS**: Runs on ports 5090 (HTTPS) and 7069 (HTTP)
- **http/https**: Original Railway deployment profiles (unchanged)

## Notes

- All existing Railway configurations remain intact
- Local environment uses `appsettings.Local.json` configuration
- Test Stripe keys are already configured for local development
- CORS is configured to allow localhost:3000 and localhost:3001 for frontend development
