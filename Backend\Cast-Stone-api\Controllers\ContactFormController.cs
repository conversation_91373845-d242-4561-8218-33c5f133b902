using Microsoft.AspNetCore.Mvc;
using Cast_Stone_api.DTOs.Request;
using Cast_Stone_api.DTOs.Response;
using Cast_Stone_api.Services.Interfaces;

namespace Cast_Stone_api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ContactFormController : ControllerBase
{
    private readonly IContactFormSubmissionService _contactFormService;
    private readonly ILogger<ContactFormController> _logger;

    public ContactFormController(IContactFormSubmissionService contactFormService, ILogger<ContactFormController> logger)
    {
        _contactFormService = contactFormService;
        _logger = logger;
    }

    /// <summary>
    /// Get all contact form submissions (Admin only)
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<IEnumerable<ContactFormSubmissionResponse>>>> GetAll()
    {
        try
        {
            var submissions = await _contactFormService.GetAllAsync();
            return Ok(ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.SuccessResponse(submissions));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.ErrorResponse("Internal server error", new List<string> { ex.ToString() }));
        }
    }

    /// <summary>
    /// Get contact form submission by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<ContactFormSubmissionResponse>>> GetById(int id)
    {
        try
        {
            var submission = await _contactFormService.GetByIdAsync(id);
            if (submission == null)
            {
                return NotFound(ApiResponse<ContactFormSubmissionResponse>.ErrorResponse("Contact form submission not found"));
            }
            return Ok(ApiResponse<ContactFormSubmissionResponse>.SuccessResponse(submission));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ContactFormSubmissionResponse>.ErrorResponse("Internal server error", new List<string> { ex.ToString() }));
        }
    }

    /// <summary>
    /// Create a new contact form submission
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<ContactFormSubmissionResponse>>> Create([FromBody] CreateContactFormSubmissionRequest request)
    {
        _logger.LogInformation("📝 Received contact form submission from {Email} ({Name})", request.Email, request.Name);

        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("❌ Contact form validation failed for {Email}. Errors: {Errors}",
                    request.Email, string.Join(", ", errors));
                return BadRequest(ApiResponse<ContactFormSubmissionResponse>.ErrorResponse("Validation failed", errors));
            }

            _logger.LogDebug("📋 Contact form details - Name: {Name}, Email: {Email}, Company: {Company}, State: {State}, Inquiry: {Inquiry}",
                request.Name, request.Email, request.Company ?? "N/A", request.State, request.Inquiry);

            var submission = await _contactFormService.CreateAsync(request);

            _logger.LogInformation("✅ Contact form submitted successfully with ID: {SubmissionId} for {Email}",
                submission.Id, request.Email);

            return CreatedAtAction(nameof(GetById), new { id = submission.Id },
                ApiResponse<ContactFormSubmissionResponse>.SuccessResponse(submission, "Contact form submitted successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("⚠️ Contact form argument error for {Email}: {Message}", request.Email, ex.Message);
            return BadRequest(ApiResponse<ContactFormSubmissionResponse>.ErrorResponse(ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Unexpected error processing contact form for {Email}", request.Email);
            return StatusCode(500, ApiResponse<ContactFormSubmissionResponse>.ErrorResponse("Internal server error", new List<string> { ex.ToString() }));
        }
    }

    /// <summary>
    /// Get recent contact form submissions
    /// </summary>
    [HttpGet("recent")]
    public async Task<ActionResult<ApiResponse<IEnumerable<ContactFormSubmissionResponse>>>> GetRecent([FromQuery] int count = 10)
    {
        try
        {
            var submissions = await _contactFormService.GetRecentSubmissionsAsync(count);
            return Ok(ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.SuccessResponse(submissions));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.ErrorResponse("Internal server error", new List<string> { ex.ToString() }));
        }
    }

    /// <summary>
    /// Get contact form submissions by inquiry type
    /// </summary>
    [HttpGet("inquiry/{inquiryType}")]
    public async Task<ActionResult<ApiResponse<IEnumerable<ContactFormSubmissionResponse>>>> GetByInquiryType(int inquiryType)
    {
        try
        {
            var submissions = await _contactFormService.GetSubmissionsByInquiryTypeAsync(inquiryType);
            return Ok(ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.SuccessResponse(submissions));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.ErrorResponse("Internal server error", new List<string> { ex.ToString() }));
        }
    }

    /// <summary>
    /// Get contact form submissions by date range
    /// </summary>
    [HttpGet("date-range")]
    public async Task<ActionResult<ApiResponse<IEnumerable<ContactFormSubmissionResponse>>>> GetByDateRange([FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
    {
        try
        {
            var submissions = await _contactFormService.GetSubmissionsByDateRangeAsync(startDate, endDate);
            return Ok(ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.SuccessResponse(submissions));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<IEnumerable<ContactFormSubmissionResponse>>.ErrorResponse("Internal server error", new List<string> { ex.ToString() }));
        }
    }
}
