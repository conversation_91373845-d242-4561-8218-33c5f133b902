# Email Debugging Guide - Cast Stone API

This guide will help you debug the Brevo email integration issue with comprehensive logging.

## 🔧 Enhanced Logging Features

### 1. **Detailed SMTP Logging**
- Connection attempts and results
- Authentication status
- Email sending process step-by-step
- Error categorization (connection, authentication, sending)

### 2. **Contact Form Flow Logging**
- Form submission received
- Validation results
- Database save status
- Email trigger attempts
- Success/failure notifications

### 3. **Configuration Logging**
- SMTP settings verification (without sensitive data)
- Environment detection
- Configuration loading status

## 🚀 How to Debug Email Issues

### Step 1: Check Configuration at Startup
When you run the API, you'll see startup logs like:
```
🧪 ENVIRONMENT: Local
📧 SMTP Configuration:
   Host: smtp-relay.brevo.com
   Port: 587
   FromEmail: <EMAIL>
   Has Username: True
   Has Password: True
```

### Step 2: Test Email Configuration
Use the new test endpoint:
```bash
GET /api/email/test-config
```
This will show your current SMTP configuration without exposing sensitive data.

### Step 3: Test Basic Email Sending
Use the test email endpoint:
```bash
POST /api/email/test-send
{
  "email": "<EMAIL>"
}
```

### Step 4: Submit a Contact Form
Submit a contact form and watch the logs for the complete flow:

1. **Form Submission**: `📝 Received contact form submission from {email}`
2. **Validation**: `📋 Contact form details - Name: {name}, Email: {email}...`
3. **Database Save**: `✅ Contact form submitted successfully with ID: {id}`
4. **Email Trigger**: `📧 Attempting to send auto-reply email to {email}`
5. **Email Process**: Detailed SMTP connection and sending logs
6. **Result**: Success or failure with specific error details

## 📊 Log Levels and What They Mean

### Information Level (🔵)
- Normal operation flow
- Successful operations
- Configuration status

### Debug Level (🟡)
- Detailed step-by-step process
- SMTP connection details
- Email content information

### Warning Level (🟠)
- Non-critical issues
- Email failures that don't break the form submission
- Validation warnings

### Error Level (🔴)
- Critical failures
- SMTP connection errors
- Authentication failures
- Unexpected exceptions

## 🔍 Common Issues and Log Patterns

### 1. **Authentication Issues**
Look for logs like:
```
🔐 SMTP Authentication Error - Check SMTP credentials
🔐 Authentication Error - Check SMTP credentials
🔐 Possible Authentication Issue - Check Brevo credentials
```

### 2. **Connection Issues**
Look for logs like:
```
🔌 Network Error - SocketErrorCode: {code}
🔌 Possible Connection Issue - Check network and SMTP settings
📧 SMTP Connection Error - Service not connected
```

### 3. **Configuration Issues**
Look for logs like:
```
❌ Error retrieving email configuration
Has Username: False
Has Password: False
```

## 🛠️ Debugging Steps

### 1. **Enable Debug Logging**
The `appsettings.Local.json` has been configured with debug-level logging for:
- EmailService
- ContactFormController
- EmailController
- ContactFormSubmissionService
- MailKit components

### 2. **Check Brevo Credentials**
Verify in your logs that:
- Username is set: `Has Username: True`
- Password is set: `Has Password: True`
- FromEmail matches your Brevo sender: `<EMAIL>`

### 3. **Test Network Connectivity**
The logs will show if the API can connect to `smtp-relay.brevo.com:587`

### 4. **Verify Email Content**
Debug logs will show:
- Email subject
- HTML and text body lengths
- Recipient email address

## 📝 Log Examples

### Successful Email Flow:
```
🚀 Starting email send <NAME_EMAIL> with subject: Thank you for contacting Cast Stone
📧 SMTP Configuration - Host: smtp-relay.brevo.com, Port: 587, EnableSsl: True
📝 Email message created - From: <EMAIL>, To: <EMAIL>
🔌 Connecting to SMTP server: smtp-relay.brevo.com:587
✅ Connected to SMTP server successfully
🔐 Authenticating with username: <EMAIL>
✅ SMTP authentication successful
📤 Sending email message...
✅ Email sent <NAME_EMAIL>
```

### Failed Email Flow:
```
🚀 Starting email send <NAME_EMAIL>
❌ Error sending <NAME_EMAIL>. Exception Type: AuthenticationException
🔐 Authentication Error - Check SMTP credentials
```

## 🎯 Next Steps

1. **Run the API** with the enhanced logging
2. **Submit a test contact form** 
3. **Check the console output** for detailed logs
4. **Use the test endpoints** to isolate issues
5. **Compare your logs** with the examples above

The enhanced logging will help identify exactly where the email process is failing and provide specific guidance on how to fix it.
